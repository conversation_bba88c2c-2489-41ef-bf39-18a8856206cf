<?php

namespace App\Http\Controllers\Website;

use App\Helper\CacheHelper;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\ContactUsResource;
use App\Http\Requests\Website\ContactUsRequest;
use App\Http\Resources\Website\BannerResource;
use App\Models\Banner;
use App\Repositories\Banner\BannerRepositoryInterface;
use App\Repositories\ContactUs\ContactUsRepositoryInterface;
use Illuminate\Http\JsonResponse;


class BannerController extends ApiBaseController
{
    /**
     * @param  BannerRepositoryInterface $bannerRepository
     * @param  Banner $model
     * @param  CacheHelper $cacheHelper
     */
    public function __construct(
        protected BannerRepositoryInterface $bannerRepository,
        protected Banner $model,
        protected CacheHelper $cacheHelper,
        public int $minutes = 10
    ) {
    }
    /**
     * Store a new ContactUs record .
     * @param ContactUsRequest $request
     * @return JsonResponse
     */
    public function index()
    {

        $keyCache = $this->bannerRepository->getCacheKey();

        $data = $this->cacheHelper->remember(
            key: $keyCache,
            callback: function () {
                return $this->bannerRepository->query()
                    ->isPublished()
                    ->with(['media'])
                    ->get();
            },
            tags: [],
            ttl: $this->minutes,
        );

        return $this->sendSuccess(BannerResource::generate($data));
    }


}