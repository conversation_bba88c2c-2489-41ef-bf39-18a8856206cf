<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Helper\CacheHelper;
use App\Models\Filters\BrandLabelsFilter;
use App\Models\Filters\InHomePageFilter;
use App\Traits\Models\CustomInteractsWithMedia;
use Spatie\MediaLibrary\HasMedia;
use App\Traits\HasTranslations;
use Carbon\Carbon;
use App\Traits\Models\Lookable;
use App\Models\Filters\UsedCategoriesBrandsFilter;
use App\Models\Filters\CategoriesBrandsFilter;
use App\Models\Filters\SearchFilter;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\Models\Searchable;
use App\Traits\Models\HasFilters;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * Class Brand
 *
 * @property int $brandId
 * @property int $name
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 * @property Collection|Variance[] $variances
 *
 * @package App\Models
 */

class Brand extends BaseModel implements HasMedia
{
	use HasTranslations, HasFactory, Lookable, Searchable, HasFilters, CustomInteractsWithMedia;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'brands';
	protected $hidden = ['pivot'];

	protected $primaryKey = 'brandId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	public $translatable = ['name', 'metaTitle', 'metaDescription', 'oldSlug'];
	public $imagesCollection = ['logo', 'logoName'];

	public $keyCacheShared = 'shared.brands';
	public $patternCache = '*brands*';
	protected CacheHelper $cacheHelper;
	public int $minutes = 10;

	protected $casts = [
		'name' => 'json',
		'slug' => 'string',
		'metaTitle' => 'json',
		'metaDescription' => 'json',
		'inHomePage' => 'boolean',
		'oldSlug' => 'json',
		'filtersGroupsId' => "int",
		'mediaCollections' => 'array'
	];

	protected $fillable = [
		'name',
		'slug',
		'inHomePage',
		'publishedAt',
		'unPublishedAt',
		'isPublished',
		'metaTitle',
		'metaDescription',
		'filtersGroupsId',
		'mediaCollections'
	];

	public function __construct(array $attributes = [])
	{
		parent::__construct($attributes);

		// Initialize the cacheHelper here
		$this->cacheHelper = app(CacheHelper::class); // Assuming CacheHelper is bound in the service container
	}



	public function resetCache()
	{

		$this->cacheHelper->deletePattern($this->patternCache);
		$this->cacheHelper->remember(
			key: $this->keyCacheShared,
			callback: function () {
				return $this->filters()
					->get()->toArray();
			},
			tags: [],
			ttl: $this->minutes,
		);
	}

	public static function boot()
	{
		Brand::observe(\App\Observers\BrandObserver::class);
		parent::boot();

	}

	public function variances(): HasMany
	{
		return $this->hasMany(Variance::class, 'brandId');
	}
	public function products(): HasMany
	{
		return $this->hasMany(Product::class, 'productId');
	}

	public function categories(): BelongsToMany
	{
		return $this->belongsToMany(Category::class, 'categories_brands', 'brandId', 'categoryId')
			->withPivot('categoryBrandId')
			->withTimestamps();
	}
	public function usedCategories(): BelongsToMany
	{
		return $this->belongsToMany(UsedCategory::class, 'used_categories_brands', 'brandId', 'usedCategoryId')
			->withPivot('usedCategoryBrandId')
			->withTimestamps();
	}


	public function getLookupResourceConfig(): array
	{
		return [
			'text_column' => 'name',
			'value_column' => 'brandId',
			'meta' => [
				'media',
				'slug',
			]
		];
	}
	public function allowedFilters(): array
	{
		return [
			'used-categories' => UsedCategoriesBrandsFilter::class,
			'categories' => CategoriesBrandsFilter::class,
			'search' => SearchFilter::class,
			'inHomePage' => InHomePageFilter::class,
			'labels' => BrandLabelsFilter::class
		];


	}

	public function allowedSearchAttributes(): array
	{
		return [
			'name->ar',
			'name->en',
			'brandId',
			'inHomePage',
			'createdAt',
			'updatedAt',

		];
	}



	public function logo()
	{
		return $this->morphOne(Media::class, 'model')
			->where('collection_name', 'logo');
	}


	public function logoName()
	{
		return $this->morphOne(Media::class, 'model')
			->where('collection_name', 'logoName');
	}





	public function brandLabels(): HasMany
	{
		return $this->hasMany(BrandLabel::class, 'brandId');
	}


	public function labels(): HasManyThrough
	{
		return $this->hasManyThrough(
			Label::class,           // Final model
			BrandLabel::class,   // Intermediate model
			'brandId',          // Foreign key on intermediate table
			'labelId',             // Foreign key on final table
			'brandId',          // Local key on this table
			'labelId'              // Local key on intermediate table
		);
	}



}