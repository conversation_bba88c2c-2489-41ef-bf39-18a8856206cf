<?php

namespace App\Models\Filters\MeilisearchFilters;

use App\Filters\Interfaces\MeilisearchFiltersInterface;
use App\Filters\Interfaces\WithOptions;
use App\Filters\Interfaces\WithType;
use App\Helper\CacheHelper;
use App\Http\Resources\OptionsResource;
use App\Models\Brand;
use App\Models\Category;



class BrandIdFilter extends BaseFilter implements MeilisearchFiltersInterface, WithOptions, WithType
{
    public $facet = [];
    private $filter;

    private $minutes = 10;
    function __construct($filter)
    {
        $this->filter = $filter;
        parent::__construct($filter);

    }
    public function handle(): string
    {
        return $this->parse($this->getValue());
    }

    public function getValue(): mixed
    {
        $key = $this->getKey();
        $value = request()->has($key) && !blank(request()->get($key)) ? explode(",", request()->get($key)) : null;
        return collect($value)->map(function ($item) {
            return (int) trim($item);
        })->toArray();
    }

    public function getOptions(): OptionsResource
    {

        $cacheHelper = new CacheHelper;

        $brands = $cacheHelper->remember(
            key: "brands-filters",
            callback: function () {
                return Brand::all();
            },
            tags: [],
            ttl: $this->minutes,
        );

        $brands = $brands->map(function ($option, $name) {
            // dd($name, $this->facet, $option->categoryId, isset($this->facet[$option->categoryId]));
            $option->facet = $this->facetsOptions[(int) $option->brandId] ?? 0;
            $option->type = 'brandId';
            return $option;
        });

        return new OptionsResource(resource: $brands, textColumn: 'name', valueColumn: 'brandId', meta: ['facet', 'type']);
    }

    static function parse($value): string
    {
        if (is_integer($value) || is_string($value) || count($value) === 1) {
            return 'brandId = ' . $value[0];
        } else if (count($value)) {
            return 'brandId IN [' . join(',', $value) . ']';
        }
        return '';
    }

    public function getFacetName(): string
    {
        return 'brandId';
    }

    public function setFacet($value)
    {
        $this->facet = $value;
    }





}