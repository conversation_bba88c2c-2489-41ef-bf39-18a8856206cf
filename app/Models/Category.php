<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\Models\CustomInteractsWithMedia;
use Carbon\Carbon;
use App\Models\FilterGroup;
use App\Filters\SearchFilter;
use App\Helper\CacheHelper;
use App\Traits\HasTranslations;
use App\Traits\Models\Lookable;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Spatie\MediaLibrary\HasMedia;
use App\Models\Filters\OrderByFilter;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Collection;
use App\Models\Filters\CategoriesBrandsFilter;
use App\Models\Filters\isFeaturedFilter;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Laravel\Scout\Searchable as ScoutSearchable;
use Illuminate\Support\Str;

/**
 * Class Category
 *
 * @property int $categoryId
 * @property String $name
 * @property int|null $parentId
 * @property string $type
 * @property Carbon $HasMedia
 * @property Carbon $updatedAt
 *
 * @property Category|null $category
 * @property Collection|Category[] $categories
 * @property Collection|CategoriesAttribute[] $categoriesAttributes
 * @property Collection|Product[] $products
 *
 * @package App\Models
 */
class Category extends BaseModel implements HasMedia
{

    use HasTranslations, HasFactory, Lookable, HasFilters, CustomInteractsWithMedia, Searchable, ScoutSearchable;


    const UPDATED_AT = 'updatedAt';
    const CREATED_AT = 'createdAt';
    protected $table = 'categories';
    protected $primaryKey = 'categoryId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['name', 'metaTitle', 'metaDescription', 'oldSlug'];
    public $imagesCollection = 'cover';

    public $keyCacheShared = 'shared.categories';
    public $patternCache = '*categories*';
    protected CacheHelper $cacheHelper;
    public int $minutes = 10;

    protected $casts = [
        'parentId' => 'int',
        'name' => 'json',
        'slug' => 'string',
        'metaTitle' => 'json',
        'metaDescription' => 'json',
        'filtersGroupsId' => "int",
        'sort' => 'int',
        'priority' => 'int',
        'oldSlug' => 'json',
        'isShowBrandIngListing' => 'bool',
        'isFeatured' => 'bool'
    ];

    protected $fillable = [
        'name',
        'parentId',
        'slug',
        'metaTitle',
        'metaDescription',
        'filtersGroupsId',
        'sort',
        'priority',
        'isShowBrandIngListing',
        'isFeatured'
    ];


    public $withRelation = [
        'media',
        'parent.media',
        'children',
        'children.media',
        'children.children',
        'children.children.media',
        'children.children.children',
        'children.children.children.media',
    ];


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // Initialize the cacheHelper here
        $this->cacheHelper = app(CacheHelper::class); // Assuming CacheHelper is bound in the service container
    }


    public function resetCache()
    {
        $this->cacheHelper->deletePattern($this->patternCache);
        $this->cacheHelper->remember(
            key: $this->keyCacheShared,
            callback: function () {
                return $this->orderBy('categories.sort', 'DESC')->with($this->withRelation)
                    ->filters()
                    ->whereNull('parentId')

                    ->get()->toArray();
            },
            tags: [],
            ttl: $this->minutes,
        );
    }
    public static function boot()
    {

        parent::boot();

        static::creating(function ($model) {
            $model->resetCache();
        });

        static::updating(function ($model) {
            $model->resetCache();
        });

        static::saved(function ($model) {
            $model->resetCache();
        });

        static::deleted(function ($model) {
            $model->resetCache();
        });

    }

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parentId')->orderBy('sort', 'DESC');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parentId')->orderBy('sort', 'DESC');
    }



    public function categoriesAttributes()
    {
        return $this->hasMany(CategoriesAttribute::class, 'categoryId');

    }


    // public function categoriesAttributes(): BelongsToMany
    // {
    //     return $this->belongsToMany(Attribute::class, 'categories_attributes', 'categoryId', 'attributeId')
    //         ->withPivot('categoryAttributeId')
    //         ->withTimestamps();
    // }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'categories_attributes', 'categoryId', 'attributeId')
            ->withPivot('categoryAttributeId')
            ->withTimestamps();
    }

    // public function categoriesBrands(): BelongsToMany
    // {
    //     return $this->belongsToMany(Brand::class, 'categories_brands', 'categoryId', 'brandId')
    //         ->withPivot('categoryBrandId')
    //         ->withTimestamps();

    // }


    public function categoriesBrands()
    {
        return $this->hasMany(CategoriesBrand::class, 'categoryId');

    }


    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class, 'categories_brands', 'categoryId', 'brandId')
            ->withPivot('categoryBrandId')
            ->withTimestamps();
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'categoryId')->withPivot('productId');
    }


    public function filterGroup()
    {
        return $this->hasMany(FilterGroup::class, 'filtersGroupsId');
    }

    public function categoriesMedia(): HasMany
    {
        return $this->hasMany(CategoriesMedia::class, 'categoryId');
    }

    public function categoriesProducts()
    {
        return $this->belongsToMany(Product::class, 'categories_products', 'categoryId', 'productId')
            ->withPivot('categoryProductId')
            ->withTimestamps();
    }

    public function groupFilters()
    {
        return $this->hasOne(FilterGroup::class, 'filtersGroupsId', 'filtersGroupsId');
    }

    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'categoryId',
            'meta' => [
                'slug',
                'children',
                'media',
                'sort'
            ]
        ];
    }

    public function allowedFilters(): array
    {
        return [
            'search' => SearchFilter::class,
            'isFeatured' => isFeaturedFilter::class,
            'order_by' => OrderByFilter::class,
        ];
    }

    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'categoryId',
            'createdAt',
            'updatedAt',

        ];
    }




    protected function makeAllSearchableUsing($query)
    {
        return $query->with(['filterGroup', 'categoriesAttributes']);
    }

    public function toSearchableArray()
    {
        $this->translationLocale = null;

        $this->translatable = [];

        // $categoriesAttributes = $this->load('categoriesAttributes')->toArray();

        $array = $this->toArray();
        $result = [];

        $result['categoryId'] = $array['categoryId'];
        $result['name'] = $array['name'];

        return $result;
    }



    public static function generateSlug(string $name, ?int $parentId = null): string
    {
        // Normalize the name to lowercase and generate a base slug
        $name = strtolower($name);
        $slug = Str::slug($name);
        $slug = str_replace("--", "-", $slug);
        // query count
        $queryCount = static::where('slug', '=', $slug);
        if (!is_null($parentId)) {
            $queryCount->where('parentId', '=', $parentId);
        } else {
            $queryCount->whereNull('parentId');
        }
        $count = $queryCount->count();

        // condation loop
        $loop = $count ? true : false;
        $index = 1;
        while ($loop) {
            $newSlug = $slug . "-$index";
            // query count
            $queryCount = static::where('slug', '=', $newSlug);
            if (!is_null($parentId)) {
                $queryCount->where('parentId', '=', $parentId);
            } else {
                $queryCount->whereNull('parentId');
            }
            $count = $queryCount->count();

            if ($count == 0) {
                $slug = $newSlug;
                $loop = false;
            }
            $index++;
        }

        return $slug;


    }




    public function categoryLabels(): HasMany
    {
        return $this->hasMany(CategoryLabel::class, 'categoryId');
    }



    public function labels(): HasManyThrough
    {
        return $this->hasManyThrough(
            Label::class,           // Final model
            CategoryLabel::class,   // Intermediate model
            'categoryId',          // Foreign key on intermediate table
            'labelId',             // Foreign key on final table
            'categoryId',          // Local key on this table
            'labelId'              // Local key on intermediate table
        );
    }

    public function cover()
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', 'cover');
    }


}