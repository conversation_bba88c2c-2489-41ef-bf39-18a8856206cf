<?php

namespace App\Observers;

use App\Http\Resources\General\MediaResource;
use App\Models\Brand;

class BrandObserver
{


    public function saved(Brand $brand)
    {
        // $brand->mediaCollections = MediaResource::generate($brand);
        $brand->resetCache();

    }

    public function deleted(Brand $brand)
    {
        $brand->resetCache();
        $brand->mediaCollections = MediaResource::generate($brand);
    }

    public function restored(Brand $brand)
    {
        $brand->mediaCollections = MediaResource::generate($brand);
        $brand->resetCache();

    }
    public function forceDeleted(Brand $brand)
    {
        $brand->mediaCollections = MediaResource::generate($brand);
        $brand->resetCache();
    }

    public function created(Brand $brand)
    {
        $brand->mediaCollections = MediaResource::generate($brand);
        $brand->resetCache();
    }

    public function updated(Brand $brand)
    {
        $brand->mediaCollections = MediaResource::generate($brand);
        $brand->resetCache();
    }


}