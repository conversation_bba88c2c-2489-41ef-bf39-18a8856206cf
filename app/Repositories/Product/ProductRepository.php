<?php

namespace App\Repositories\Product;

use App\Enums\VarianceTypeEnum;
use App\Helper\Currency;
use App\Helper\SlugHelper;
use App\Helper\SortableHelper;
use App\Models\ActivityLog;
use App\Models\ActivityProduct;
use App\Models\MigrationProduct;
use App\Models\PaymentsMethod;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Repositories\Rating\RatingRepositoryInterface;
use Carbon\Carbon;
use App\Models\Stock;
use App\Models\Bundle;
use App\Models\Rating;
use App\Models\Product;
use App\Models\Variance;
use App\Models\Attribute;
use App\Models\AttributesValue;
use App\Models\ProductsAttribute;
use App\Models\ProductVisit;
use App\Models\User;
use App\Models\Visitor;
use Illuminate\Support\Facades\DB;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\Product\ProductRepositoryInterface;
use Spatie\Activitylog\Facades\LogBatch;
use Illuminate\Support\Str;

class ProductRepository extends BaseRepository implements ProductRepositoryInterface
{

    public $modelClass = Product::class;

    public $relationsDefault = [
        // 'variationAttributes',
        // 'variationAttributes.options',
        // 'variationAttributes.value',
        'variance',
        'variance.attributes',
        'variance.attributes.options',
        'variance.stocks',

        'variance.media',
        "bundle",
        'bundle.stocks',
        'bundle.media',
        'bundle.stocks',
        'brand',
        'categories',
        'media'
    ];


    public $relations = [
        'bundles',
        'bundles.variance',
        'stocks',
        'variances',
        'variances.attributes',
        'variances.attributes.options',
        'variances.stocks',
        'variances.media',
        "bundles",
        'bundles.stocks',
        'bundles.media',
        'bundles.stocks',
        'brand',
        'categories',
        'media',
    ];

    public function getProductByIds(array $ids)
    {


        $with = [
            'attributes',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'default.activeStock',
            'default.media',
            'media',

        ];
        return $this->query()->with($with)->whereIn('productId', $ids)->customPaginate();
    }

    public function storeProduct(array $data): Model
    {
        ini_set('upload_max_filesize', '2048M');
        ini_set('post_max_size', '2048M');
        ini_set('max_execution_time', 500);
        $product = $this->save($data);
        return $product;
    }





    private function save($data)
    {
        $product = null;
        LogBatch::startBatch();
        DB::transaction(function () use ($data, &$product) {
            $slug = Str::slug($data['name']['en'], '-');
            $slug = str_replace("--", "-", $slug);

            $metaTitle = [
                'en' => isset($data['metaTitle']['en'])
                    ? str_replace('{name}', $data['name']['en'] ?? '', $data['metaTitle']['en'])
                    : null,
                'ar' => isset($data['metaTitle']['en'])
                    ? str_replace('{name}', $data['name']['ar'] ?? '', $data['metaTitle']['ar'])
                    : null,
            ];

            $metaDescription = [
                'en' => isset($data['metaDescription']['en'])
                    ? str_replace('{name}', $data['name']['en'] ?? '', $data['metaDescription']['en'])
                    : null,
                'ar' => isset($data['metaDescription']['en'])
                    ? str_replace('{name}', $data['name']['ar'] ?? '', $data['metaDescription']['ar'])
                    : null,
            ];


            $product = $this->store([
                'name' => $data['name'],
                'slug' => SlugHelper::slugChecker($slug, Product::getTableName()),
                'description' => $data['description'],
                'type' => $data['type'],
                'isPublished' => $data['isPublished'],
                'isListed' => $data['isListed'],
                'variationAttributes' => join(',', $data['variationAttributes']),
                'releaseAt' => $data['releaseAt'] ?? null,
                "brandId" => $data['brandId'] ?? null,
                "priority" => $data['priority'] ?? 0,
                "metaTitle" => $metaTitle,
                "metaDescription" => $metaDescription,

            ]);

            $product->categoriesProduct()->sync($data['categories']);


            if (isset($data['attributes']) && is_array($data['attributes'])) {

                $attrRepo = resolve(AttributeRepositoryInterface::class);
                $attributes = collect($data['attributes'])->map(function ($attribute) use ($attrRepo) {
                    $attr['attributeId'] = $attribute['attributeId'];
                    $attrOb = $attrRepo->findById($attribute['attributeId']);
                    if ($attrOb->type == 'text' || $attrOb->type == 'textarea') {
                        $attr['attributeOptionId'] = null;
                        $attr['value'] = $attribute['attributeOptionId'];

                    } else {
                        $attr['attributeOptionId'] = $attribute['attributeOptionId'];
                    }
                    return $attr;
                })->toArray();

                $product->attributeValues()->sync($attributes);
                $product->load('attributeValues');
                $product->productAttributes()->sync($product->attributeValues->map(function ($attribute) use ($product) {
                    return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId, 'productId' => $product->productId];
                }));

            }


            $product->disabledPaymentMethods()->sync(collect($data['disabledPaymentMethods'] ?? [])->map(function ($item) {
                return ['paymentMethodId' => $item];

            }));

            $product->productShippings()->sync(collect($data['shippingCarriers'] ?? [])->map(function ($item) {
                return ['shippingCarrierId' => $item];
            }));

            $product->productLabels()->sync(collect($data['labels'] ?? [])->map(function ($item) {
                return ['labelId' => $item];
            }));


            if ($data["type"] === 'alternative') {

                foreach ($data['variances'] as $keyVariance => $variance) {
                    $slug = Str::slug($variance['name']['en'], '-');
                    $slug = str_replace("--", "-", subject: $slug);
                    $varianceModel = $product->variance()->create([
                        "name" => ['en' => $data['name']['en'] . " " . $variance['name']['en'], 'ar' => $data['name']['ar'] . " " . $variance['name']['ar']],
                        'slug' => SlugHelper::slugCheckerWithinCondition($slug, Variance::getTableName(), ['productId' => $product->productId]),
                        'isDefault' => $variance['isDefault'] ?? false,
                        "brandId" => $data['brandId'] ?? null,
                        "SKU" => $variance['SKU'] ?? null,
                        "type" => $variance['type'],
                        // "description" => $variance['description'] ?? null,
                        "modelNumber" => $variance['modelNumber'] ?? null,
                        "metaTitle" => $variance['metaTitle'],
                        "metaDescription" => $variance['metaDescription'],
                        "publishedAt" => $variance['publishedAt'] ?? null,
                        'isPublished' => $variance['isPublished'] ?? 0,
                        "unPublishedAt" => $variance['unPublishedAt'] ?? null,
                        "auctionId" => $variance['auctionId'],
                    ]);


                    $stocks = isset($variance['stocks']) ? $variance['stocks'] : [];
                    $stocks = SortableHelper::sort($stocks);
                    foreach ($stocks as $stock) {

                        $stockModel = Stock::create([
                            'quantity' => $stock['quantity'],
                            'maxPerUser' => $stock['maxPerUser'] ?? 1,
                            'publishedAt' => $stock['publishedAt'] ?? null,
                            'unPublishedAt' => $stock['unPublishedAt'] ?? null,
                            "isPublished" => $stock['isPublished'] ?? 1,
                            'supplierId' => $stock['supplierId'],
                            'sort' => $stock['sort'],
                            'isOffer' => $stock['isOffer'],
                            'price' => Currency::convert($stock['price']['value'])->from($stock['price']['currencyId'])->toBase()->get(),
                            'cost' => Currency::convert($stock['cost']['value'])->from($stock['cost']['currencyId'])->toBase()->get(),
                            'priceBeforeOffer' => $stock['isOffer'] == true ? Currency::convert($stock['priceBeforeOffer']['value'])->from($stock['priceBeforeOffer']['currencyId'])->toBase()->get() : null,
                            'isPreOrder' => $stock['isPreOrder'] ?? false,
                            'note' => $stock['note'] ?? null,
                        ]);

                        $varianceModel->variancesStocks()->sync([$stockModel->stockId => ['varianceId' => $varianceModel->varianceId]]);

                    }

                    // JobsHelper::dispatchSync($product, $stocks);

                    //$varianceAttributes = ArrayHelper::mapFormateded($variance['attributes'] ?? [], $variance->varianceId, 'varianceId', 'attributeId');


                    if (isset($variance['attributes']) && is_array($variance['attributes'])) {
                        $varianceModel->attributeValues()->sync($variance['attributes']);
                        $varianceModel->varianceAttributes()->sync($varianceModel->attributeValues->map(function ($attribute) {
                            return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId];
                        }));
                    }


                    $collections = SortableHelper::sortCollecttion($variance["media"]);
                    foreach ($collections as $keyMedia => $collection) {
                        foreach ($collection as $media) {
                            $varianceModel->addMediaToModel($media, $keyMedia);
                        }
                    }

                    //$varianceModel->searchableUpdate();

                }
            }



            if ($data["type"] === 'simple') {
                $variance = $data['variances'][0];

                $slug = Str::slug($variance['name']['en'], '-');
                $slug = str_replace("--", "-", $slug);
                $varianceModel = $product->variance()->create([
                    "name" => ['en' => $data['name']['en'] . " " . $variance['name']['en'], 'ar' => $data['name']['ar'] . " " . $variance['name']['ar']],
                    "slug" => SlugHelper::slugChecker($slug, Variance::getTableName()),
                    "brandId" => $data['brandId'] ?? null,
                    'isDefault' => true,
                    "SKU" => $data['SKU'] ?? null,
                    "type" => $variance['type'],
                    "metaTitle" => $metaTitle,
                    "metaDescription" => $metaDescription,
                    "publishedAt" => $variance['publishedAt'] ?? null,
                    'isPublished' => $variance['isPublished'] ?? 0,
                    "unPublishedAt" => $variance['unPublishedAt'] ?? null,
                    "modelNumber" => $data['modelNumber'] ?? null,
                    "auctionId" => $variance['auctionId'],


                    // "description" => $data['description'],
                ]);


                $stocks = isset($variance['stocks']) ? $variance['stocks'] : [];
                $stocks = SortableHelper::sort($stocks);

                foreach ($stocks as $stock) {


                    $stockModel = Stock::create([
                        'quantity' => $stock['quantity'],
                        'maxPerUser' => $stock['maxPerUser'] ?? 1,
                        'publishedAt' => $stock['publishedAt'],
                        'unPublishedAt' => $stock['unPublishedAt'],
                        "isPublished" => $stock['isPublished'],
                        'supplierId' => $stock['supplierId'],
                        'sort' => $stock['sort'],
                        'isOffer' => $stock['isOffer'],
                        'price' => Currency::convert($stock['price']['value'])->from($stock['price']['currencyId'])->toBase()->get(),
                        'cost' => Currency::convert($stock['cost']['value'])->from($stock['cost']['currencyId'])->toBase()->get(),
                        'priceBeforeOffer' => $stock['isOffer'] == true ? Currency::convert($stock['priceBeforeOffer']['value'])->from($stock['priceBeforeOffer']['currencyId'])->toBase()->get() : null,
                        'isPreOrder' => $stock['isPreOrder'] ?? false,
                        'note' => $stock['note'] ?? null,
                    ]);

                    $varianceModel->variancesStocks()->sync([$stockModel->stockId => ['varianceId' => $varianceModel->varianceId]]);
                }

                // JobsHelper::dispatchSync($product, $stocks);

                if (isset($variance['attributes']) && is_array($variance['attributes'])) {
                    $varianceModel->attributeValues()->sync($variance['attributes']);
                    $varianceModel->varianceAttributes()->sync($varianceModel->attributeValues->map(function ($attribute) {
                        return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId];
                    }));
                }




                $collections = SortableHelper::sortCollecttion($variance["media"] ?? []);
                foreach ($collections as $keyMedia => $collection) {
                    foreach ($collection as $media) {
                        $varianceModel->addMediaToModel($media, $keyMedia);
                    }
                }

                //  $varianceModel->searchableUpdate();
            }

            if ($data["type"] === 'bundle') {

                foreach ($data['bundles'] as $bundle) {
                    $varianceModel = null;
                    if (is_null($bundle['varianceId'])) {
                        $slug = Str::slug($bundle['name']['en'], '-');
                        $slug = str_replace("--", "-", $slug);
                        $varianceModel = $product->variance()->create([
                            "name" => $bundle['name'],
                            "slug" => SlugHelper::slugChecker($slug, Variance::getTableName()),
                            "brandId" => $data['brandId'] ?? null,
                            'isDefault' => $bundle['isDefault'] ?? false,
                            "SKU" => $bundle['SKU'] ?? null,
                            "type" => $variance['type'] ?? VarianceTypeEnum::physical->value,
                            "metaTitle" => $bundle['metaTitle'] ?? [],
                            "metaDescription" => $bundle['metaDescription'] ?? [],
                            "publishedAt" => $bundle['publishedAt'] ?? null,
                            'isPublished' => $bundle['isPublished'] ?? 0,
                            "unPublishedAt" => $bundle['unPublishedAt'] ?? null,
                            "modelNumber" => $bundle['modelNumber'] ?? null,
                            "auctionId" => $bundle['auctionId'],
                        ]);

                        $collections = SortableHelper::sortCollecttion($bundle["media"] ?? []);
                        foreach ($collections as $keyMedia => $collection) {
                            foreach ($collection as $media) {
                                $varianceModel->addMediaToModel($media, $keyMedia);
                            }
                        }

                    }

                    Bundle::create([
                        "varianceId" => is_null($bundle['varianceId']) ? $varianceModel->varianceId : $bundle['varianceId'],
                        "productId" => $product->productId
                    ]);

                    $stocks = isset($data['stocks']) ? $data['stocks'] : [];
                    $stocks = SortableHelper::sort(array: $stocks);
                    foreach ($stocks as $stock) {

                        $stockModel = Stock::create([
                            'quantity' => $stock['quantity'],
                            'maxPerUser' => $stock['maxPerUser'] ?? 1,
                            'publishedAt' => $stock['publishedAt'] ?? null,
                            'unPublishedAt' => $stock['unPublishedAt'] ?? null,
                            "isPublished" => $stock['isPublished'] ?? 1,
                            'supplierId' => $stock['supplierId'],
                            'sort' => $stock['sort'],
                            'isOffer' => $stock['isOffer'],
                            'price' => Currency::convert($stock['price']['value'])->from($stock['price']['currencyId'])->toBase()->get(),
                            'cost' => Currency::convert($stock['cost']['value'])->from($stock['cost']['currencyId'])->toBase()->get(),
                            'priceBeforeOffer' => $stock['isOffer'] == true ? Currency::convert($stock['priceBeforeOffer']['value'])->from($stock['priceBeforeOffer']['currencyId'])->toBase()->get() : null,
                            'isPreOrder' => $stock['isPreOrder'] ?? false,
                            'note' => $stock['note'] ?? null,
                        ]);



                        $product->productStocks()->sync([$stockModel->stockId => ['productId' => $product->productId]]);

                    }

                }
            }

            if ($data["type"] === 'alternative') {
                DB::statement("UPDATE variances AS v SET v.metaTitle = JSON_OBJECT( 'ar', CONCAT( COALESCE(( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.ar'))) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ) ), 'en', CONCAT( COALESCE( ( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.en')) ) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ) ) ), v.metaDescription = JSON_OBJECT( 'ar', CONCAT( COALESCE( ( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.ar')) ) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ), ' ✔️ تسوق اونلاين في الأردن مع أكشن موبايل ✔️ أسعار مناسبة ✔️ تصفح الموقع الآن' ), 'en', CONCAT( COALESCE( ( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.en')) ) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ), ' ✔️ Shop online in Jordan with Action Mobile ✔️ Affordable prices ✔️ Browse now' ) ) where v.productId =$product->productId;");
                DB::statement(" UPDATE variances AS v join products pr ON pr.productId = v.productId SET v.metaTitle = JSON_OBJECT( 'ar', CONCAT( JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.ar')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaTitle , '$.ar'))), 'en', CONCAT( JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.en')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaTitle , '$.en')))), v.metaDescription = JSON_OBJECT( 'ar', CONCAT(JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.ar')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaDescription , '$.ar'))), 'en', CONCAT( JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.en')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaDescription , '$.en'))) ) where pr.productId =$product->productId");
            }
            $collections = SortableHelper::sortCollecttion($data["media"]);
            foreach ($collections as $keyMedia => $collection) {
                foreach ($collection as $media) {
                    $product->addMediaToModel($media, $keyMedia);
                }
            }


            // event(new CreateUpdateProductEvent($product));
        });

        ActivityProduct::create([
            'action' => "create",
            'productId' => $product->productId,
            'userId' => auth(GUARD_API)->user()->userId,
            'properties' => $data

        ]);
        $product->touchWithCalculation();
        LogBatch::endBatch();
        return $product;
    }




    public function updateProduct(int|string $id, array $data): Model
    {

        $product = null;
        ini_set('upload_max_filesize', '2048M');
        ini_set('post_max_size', '2048M');
        ini_set('max_execution_time', 500);

        LogBatch::startBatch('batch_uuid');

        DB::transaction(function () use ($id, $data, &$product) {

            $product = $this->update($id, [
                'name' => $data['name'],
                'description' => $data['description'],
                'type' => $data['type'],
                'isPublished' => $data['isPublished'],
                'isListed' => $data['isListed'],
                'variationAttributes' => join(',', $data['variationAttributes']),
                'releaseAt' => $data['releaseAt'],
                "brandId" => $data['brandId'] ?? null,
                "priority" => $data['priority'] ?? 0,
                "metaTitle" => $data['metaTitle'] ?? null,
                "metaDescription" => $data['metaDescription'] ?? null,

            ]);


            $product->categoriesProduct()->sync($data['categories']);


            if (isset($data['attributes']) && is_array($data['attributes'])) {

                $attrRepo = resolve(AttributeRepositoryInterface::class);
                $attributes = collect($data['attributes'])->map(function ($attribute) use ($attrRepo) {
                    $attr['attributeId'] = $attribute['attributeId'];
                    $attrOb = $attrRepo->findById($attribute['attributeId']);
                    if ($attrOb->type == 'text' || $attrOb->type == 'textarea') {
                        $attr['attributeOptionId'] = null;
                        $attr['value'] = $attribute['attributeOptionId'];

                    } else {
                        $attr['attributeOptionId'] = $attribute['attributeOptionId'];
                    }
                    return $attr;
                })->toArray();

                $product->attributeValues()->sync($attributes);
                $product->load('attributeValues');
                $product->productAttributes()->sync($product->attributeValues->map(function ($attribute) use ($product) {
                    return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId, 'productId' => $product->productId];
                }));

            }


            $product->productShippings()->sync(collect($data['shippingCarriers'] ?? [])->map(function ($item) {
                return ['shippingCarrierId' => $item];
            }));

            $product->productPayments()->sync(collect($data['disabledPaymentMethods'] ?? [])->map(function ($item) {
                return ['paymentMethodId' => $item];
            }));

            $product->productLabels()->sync(collect($data['labels'] ?? [])->map(function ($item) {
                return ['labelId' => $item];
            }));


            // $product->disabledPaymentMethods()->sync($data['disabledPaymentMethods'] ?? []);

            if ($data["type"] === 'alternative') {
                foreach ($data['variances'] as $keyVariance => $variance) {


                    $dataSaved = [
                        "name" => $variance['name'],
                        "brandId" => $data['brandId'] ?? null,
                        "SKU" => $variance['SKU'] ?? null,
                        "type" => $variance['type'],
                        "metaTitle" => $variance['metaTitle'],
                        "metaDescription" => $variance['metaDescription'],
                        "publishedAt" => $variance['publishedAt'] ?? null,
                        'isPublished' => $variance['isPublished'] ?? 0,
                        "unPublishedAt" => $variance['unPublishedAt'] ?? null,
                        'isDefault' => $variance['isDefault'] ?? false,
                        "auctionId" => $variance['auctionId'],
                        "modelNumber" => $variance['modelNumber'] ?? null,
                        "productId" => $product->productId,
                    ];

                    if (is_null(value: $variance['varianceId'])) {
                        $slug = Str::slug($variance['name']['en'], '-');
                        $dataSaved["slug"] = SlugHelper::slugCheckerWithinCondition($slug, Variance::getTableName(), ['productId' => $product->productId]);
                        $dataSaved["name"] = ['en' => $data['name']['en'] . " " . $dataSaved['name']['en'], 'ar' => $data['name']['ar'] . " " . $dataSaved['name']['ar']];
                    }

                    $varianceModel = Variance::updateOrCreate([
                        "varianceId" => $variance['varianceId'],
                    ], $dataSaved);




                    $stocks = isset($variance['stocks']) ? $variance['stocks'] : [];
                    $stocks = SortableHelper::sort($stocks);

                    foreach ($stocks as $keyStock => $stock) {

                        $stockModel = Stock::updateOrCreate(
                            [
                                // Define the unique identifier(s) for the stock record
                                // For example, assuming 'id' is the primary key
                                'stockId' => $stock['stockId'] ?? null,
                            ],
                            [
                                // Set the values to be updated/inserted
                                'quantity' => $stock['quantity'],
                                'maxPerUser' => $stock['maxPerUser'] ?? 1,
                                'publishedAt' => $stock['publishedAt'] ?? null,
                                'unPublishedAt' => $stock['unPublishedAt'] ?? null,
                                "isPublished" => $stock['isPublished'] ?? 1,
                                'supplierId' => $stock['supplierId'],
                                'sort' => $stock['sort'],
                                'isOffer' => $stock['isOffer'],
                                'price' => Currency::convert($stock['price']['value'])->from($stock['price']['currencyId'])->toBase()->get(),
                                'cost' => Currency::convert($stock['cost']['value'])->from($stock['cost']['currencyId'])->toBase()->get(),
                                'priceBeforeOffer' => is_null($stock['priceBeforeOffer']) || $stock['isOffer'] == false ? null : Currency::convert($stock['priceBeforeOffer']['value'])->from($stock['priceBeforeOffer']['currencyId'])->toBase()->get(),
                                'isPreOrder' => $stock['isPreOrder'] ?? false,
                                'note' => $stock['note'] ?? null,
                            ]
                        );


                        $stocks[$keyStock]['stockId'] = $stockModel->stockId;


                    }

                    // JobsHelper::dispatchSync($product, $stocks);

                    $varianceModel->variancesStocks()->sync(collect($stocks)->map(function ($item) {
                        return ['stockId' => $item['stockId']];

                    }));


                    if (isset($variance['attributes']) && is_array($variance['attributes'])) {
                        $varianceModel->attributeValues()->sync($variance['attributes']);
                        $varianceModel->varianceAttributes()->sync($varianceModel->attributeValues->map(function ($attribute) {
                            return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId];
                        }));
                    }


                    $collections = SortableHelper::sortCollecttion($variance["media"]);
                    foreach ($collections as $keyMedia => $collection) {
                        foreach ($collection as $media) {
                            $varianceModel->addMediaToModel($media, $keyMedia);
                        }
                    }
                    $varianceModel->clearMediaExist($collections);


                    $data['variances'][$keyVariance]['varianceId'] = $varianceModel->varianceId;
                }


                Variance::whereNotIn('varianceId', array_column($data['variances'], 'varianceId'))->where('productId', $product->productId)->delete();
            }


            if ($data["type"] === 'simple') {

                $variance = $data['variances'][0];
                $variance['varianceId'] = $product->variances->first()?->varianceId ?? null;
                $dataSaved = [
                    "name" => $variance['name'],
                    "brandId" => $data['brandId'] ?? null,
                    "SKU" => $data['SKU'] ?? null,
                    "type" => $variance['type'],
                    "metaTitle" => $variance['metaTitle'],
                    "metaDescription" => $variance['metaDescription'],
                    "publishedAt" => $variance['publishedAt'] ?? null,
                    'isPublished' => $variance['isPublished'] ?? 0,
                    "unPublishedAt" => $variance['unPublishedAt'] ?? null,
                    'isDefault' => true,
                    "auctionId" => $variance['auctionId'],
                    "productId" => $product->productId,
                    "modelNumber" => $data['modelNumber'] ?? null
                ];

                if (is_null(value: $variance['varianceId'])) {
                    $slug = Str::slug($variance['name']['en'], '-');
                    $dataSaved["slug"] = SlugHelper::slugChecker($slug, Variance::getTableName());
                    $dataSaved["name"] = ['en' => $data['name']['en'] . " " . $dataSaved['name']['en'], 'ar' => $data['name']['ar'] . " " . $dataSaved['name']['ar']];
                }

                $varianceModel = Variance::updateOrCreate([
                    "varianceId" => $variance['varianceId'],
                ], $dataSaved);


                $stocks = isset($variance['stocks']) ? $variance['stocks'] : [];

                $stocks = SortableHelper::sort($stocks);

                foreach ($stocks as $keyStock => $stock) {

                    $stockModel = Stock::updateOrCreate(
                        [
                            // Define the unique identifier(s) for the stock record
                            // For example, assuming 'id' is the primary key
                            'stockId' => $stock['stockId'] ?? null,
                        ],
                        [
                            'quantity' => $stock['quantity'],
                            'maxPerUser' => $stock['maxPerUser'] ?? 1,
                            'publishedAt' => $stock['publishedAt'] ?? null,
                            'unPublishedAt' => $stock['unPublishedAt'] ?? null,
                            "isPublished" => $stock['isPublished'] ?? 1,
                            'supplierId' => $stock['supplierId'],
                            'sort' => $stock['sort'],
                            'isOffer' => $stock['isOffer'],
                            'price' => Currency::convert($stock['price']['value'])->from($stock['price']['currencyId'])->toBase()->get(),
                            'cost' => Currency::convert($stock['cost']['value'])->from($stock['cost']['currencyId'])->toBase()->get(),
                            'priceBeforeOffer' => is_null($stock['priceBeforeOffer']) || $stock['isOffer'] == false ? null : Currency::convert($stock['priceBeforeOffer']['value'])->from($stock['priceBeforeOffer']['currencyId'])->toBase()->get(),
                            'isPreOrder' => $stock['isPreOrder'] ?? false,
                            'note' => $stock['note'] ?? null,
                        ]
                    );


                    $stocks[$keyStock]['stockId'] = $stockModel->stockId;


                }
                // JobsHelper::dispatchSync($product, $stocks);

                $varianceModel->variancesStocks()->sync(collect($stocks)->map(function ($item) {
                    return ['stockId' => $item['stockId']];

                }));


                if (isset($variance['attributes']) && is_array($variance['attributes'])) {
                    $varianceModel->attributeValues()->sync($variance['attributes']);
                    $varianceModel->varianceAttributes()->sync($varianceModel->attributeValues->map(function ($attribute) {
                        return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId];
                    }));
                }


                $collections = SortableHelper::sortCollecttion($variance["media"]);
                foreach ($collections as $keyMedia => $collection) {
                    foreach ($collection as $media) {
                        $varianceModel->addMediaToModel($media, $keyMedia);
                    }
                }
                $varianceModel->clearMediaExist($collections);
                $product->variances()->where('varianceId', '!=', $varianceModel->varianceId)->delete();

            }



            if ($data["type"] === 'bundle') {

                foreach ($data['bundles'] as $keyBundle => $bundle) {
                    $varianceModel = null;
                    if (is_null($bundle['varianceId'])) {
                        $slug = Str::slug($bundle['name']['en'], '-');
                        $varianceModel = $product->variance()->create([
                            "name" => $bundle['name'],
                            "slug" => SlugHelper::slugChecker($slug, Variance::getTableName()),
                            "brandId" => $data['brandId'] ?? null,
                            'isDefault' => $bundle['isDefault'] ?? false,
                            "SKU" => $bundle['SKU'] ?? null,
                            "type" => $variance['type'] ?? VarianceTypeEnum::physical->value,
                            "metaTitle" => $bundle['metaTitle'] ?? [],
                            "metaDescription" => $bundle['metaDescription'] ?? [],
                            "publishedAt" => $bundle['publishedAt'] ?? null,
                            'isPublished' => $bundle['isPublished'] ?? 0,
                            "unPublishedAt" => $bundle['unPublishedAt'] ?? null,
                            "modelNumber" => $bundle['modelNumber'] ?? null,
                            "auctionId" => $bundle['auctionId'],
                        ]);


                        $collections = SortableHelper::sortCollecttion($bundle["media"] ?? []);
                        foreach ($collections as $keyMedia => $collection) {
                            foreach ($collection as $media) {
                                $varianceModel->addMediaToModel($media, $keyMedia);
                            }
                        }

                    }


                    $bundleModel = Bundle::updateOrCreate([
                        "bundleId" => $bundle['bundleId'] ?? null,
                    ], [
                        "varianceId" => is_null($bundle['varianceId']) ? $varianceModel->varianceId : $bundle['varianceId'],
                        "productId" => $product->productId
                    ]);

                    $data['bundles'][$keyBundle]['bundleId'] = $bundleModel->bundleId;

                    $stocks = isset($data['stocks']) ? $data['stocks'] : [];
                    $stocks = SortableHelper::sort($stocks);
                    foreach ($stocks as $keyStock => $stock) {

                        $stockModel = $stockModel = Stock::updateOrCreate(
                            [

                                'stockId' => $stock['stockId'] ?? null,
                            ],
                            [
                                'quantity' => $stock['quantity'],
                                'maxPerUser' => $stock['maxPerUser'] ?? 1,
                                'publishedAt' => $stock['publishedAt'] ?? null,
                                'unPublishedAt' => $stock['unPublishedAt'] ?? null,
                                "isPublished" => $stock['isPublished'],
                                'supplierId' => $stock['supplierId'],
                                'sort' => $stock['sort'],
                                'isOffer' => $stock['isOffer'],
                                'price' => Currency::convert($stock['price']['value'])->from($stock['price']['currencyId'])->toBase()->get(),
                                'cost' => Currency::convert($stock['cost']['value'])->from($stock['cost']['currencyId'])->toBase()->get(),
                                'priceBeforeOffer' => is_null($stock['priceBeforeOffer']) || $stock['isOffer'] == false ? null : Currency::convert($stock['priceBeforeOffer']['value'])->from($stock['priceBeforeOffer']['currencyId'])->toBase()->get(),
                                'isPreOrder' => $stock['isPreOrder'] ?? false,
                                'note' => $stock['note'] ?? null,
                            ]

                        );

                        $stocks[$keyStock]['stockId'] = $stockModel->stockId;

                    }

                    $product->productStocks()->sync(collect($stocks)->map(function ($item) {
                        return ['stockId' => $item['stockId']];

                    }));


                }

                Bundle::whereNotIn('bundleId', array_column($data['bundles'], 'bundleId'))->where('productId', $product->productId)->delete();
            }



            $collections = SortableHelper::sortCollecttion($data["media"]);
            foreach ($collections as $keyMedia => $collection) {
                foreach ($collection as $media) {
                    $product->addMediaToModel($media, $keyMedia);
                }
            }
            if ($data["type"] === 'alternative') {
                DB::statement("UPDATE variances AS v SET v.metaTitle = JSON_OBJECT( 'ar', CONCAT( COALESCE(( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.ar'))) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ) ), 'en', CONCAT( COALESCE( ( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.en')) ) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ) ) ), v.metaDescription = JSON_OBJECT( 'ar', CONCAT( COALESCE( ( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.ar')) ) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ), ' ✔️ تسوق اونلاين في الأردن مع أكشن موبايل ✔️ أسعار مناسبة ✔️ تصفح الموقع الآن' ), 'en', CONCAT( COALESCE( ( SELECT GROUP_CONCAT( DISTINCT CONCAT( JSON_UNQUOTE(JSON_EXTRACT(ao.name, '$.en')) ) SEPARATOR ', ' ) FROM attributes_values av JOIN attributes_options ao ON ao.attributeOptionId = av.attributeOptionId WHERE av.productId IS NULL AND av.varianceId = v.varianceId ) ), ' ✔️ Shop online in Jordan with Action Mobile ✔️ Affordable prices ✔️ Browse now' ) ) where v.productId =$product->productId;");
                DB::statement(" UPDATE variances AS v join products pr ON pr.productId = v.productId SET v.metaTitle = JSON_OBJECT( 'ar', CONCAT( JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.ar')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaTitle , '$.ar'))), 'en', CONCAT( JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.en')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaTitle , '$.en')))), v.metaDescription = JSON_OBJECT( 'ar', CONCAT(JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.ar')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaDescription , '$.ar'))), 'en', CONCAT( JSON_UNQUOTE(JSON_EXTRACT(pr.name, '$.en')),'-', JSON_UNQUOTE(JSON_EXTRACT(v.metaDescription , '$.en'))) ) where pr.productId =$product->productId");
            }

            $product->clearMediaExist($collections);


            // DB::afterCommit(function () use ($product) {
            //     $product->load([
            //         'variances',
            //         'variances.stocks',
            //         'variances.activeStock',
            //         'stocks',
            //         'activeStock'
            //     ]);
            //     Product::setterMaxAndMinPrice($product);
            // });

            // event(new CreateUpdateProductEvent($product));

        });

        $product->touchWithCalculation();

        ActivityProduct::create([
            'action' => "update",
            'productId' => $product->productId,
            'userId' => auth(GUARD_API)->user()->userId,
            'properties' => $data

        ]);

        LogBatch::endBatch();


        return $product;
    }



    public function storeDraftProduct(array $data): Model
    {
        $data['isList'] = false;
        $product = $this->save($data);
        return $product;
    }

    public function storeProductAttributes(object $product, int|array $data): mixed
    {
        $product = $product->attributes()->attach(new ProductsAttribute($data));
        return $product;
    }

    public function getProductAttributes(object $product)
    {
        dd('deprecated');
        return $product->attributes();
    }

    public function storeProductVariance(object $product, int|array $attributeId): mixed
    {
        if ($product->type == "bundle") {
            $product = $product->variances()->sync($attributeId);
        }
        return $product;
    }

    public function getAllProducts()
    {
        return $this->getAllLatestPaginatedFiltered(with: ['media', 'categories']);
    }

    public function findByIdProduct(int|string $id): mixed
    {
        $with = [

            'bundles',
            'bundles.variance',
            'bundles.variance.media',
            'stocks',
            'stocks.supplier',
            'attributes',
            'attributes.productAttributesValue' => function ($query) use ($id) {
                $query->where('productId', $id);
            },
            'variances.attributesWithValue',
            'variances.attributes.options',
            'variances.attributes.varianceAttributesValue' => function ($query) use ($id) {
                $query->whereIn('varianceId', function ($query) use ($id) {
                    $query->select('varianceId')->from('variances')->where('productId', $id);
                });
            },
            'variances.stocks',
            'variances.stocks.supplier',
            'variances.attributes',
            'variances.media',
            'ratings',
            'brand',
            'suggested',
            'attributesWithValue',
            'media',
            'disabledPaymentMethods',
            'categories',
            'shippingCarriers',
            'productLabels'
        ];


        return $this->findById($id, ['*'], with: $with);
    }


    public function deleteProduct(int|string $id): mixed
    {
        // TODO: need soft delete for variances
        $product = $this->findById($id);
        $product->touchWithCalculation();
        return $this->destroy($id);
    }


    public function getCategoryAttributes(int $category)
    {

        $items = Attribute::whereHas('categoriesAttributes', function ($q) use ($category) {
            $q->where('categoryId', $category);
        })->with('options')->get();
        //TODO, why it returns like this?? @dalal

        return ['items' => $items];
    }

    public function getAttributesWithParents()
    {

        return Attribute::whereHas('categoriesAttributes')->filters()->with(['options', 'parentAttributesRecursive'])->get();
    }



    public function getProductWithVariance(int|string $product, int|string $variance = null)
    {

        $date = Carbon::now();
        $this->relationsDefault["variance"] = function ($query) {
            $query->where('isDefault', true);
        };
        $this->relationsDefault["variance.stocks"] = function ($query) use ($date) {
            $query->where('stocks.quantity', '>', 0)
                ->where('stocks.quantity', '>', 'stocks.sold')
                ->where('stocks.isPublished', PUBLISHED)
                ->where('stocks.publishedAt', '<=', $date)
                ->where('stocks.unPublishedAt', '>=', $date)
                ->orderBy('stocks.sort', DESC);
        };

        $this->relationsDefault["bundle"] = function ($query) {
            $query->where('isDefault', true);
        };
        $this->relationsDefault["bundle.stocks"] = function ($query) use ($date) {
            $query->where('stocks.quantity', '>', 0)
                ->where('stocks.quantity', '>', 'stocks.sold')
                ->where('stocks.isPublished', PUBLISHED)
                ->where('stocks.publishedAt', '<=', $date)
                ->where('stocks.unPublishedAt', '>=', $date)
                ->orderBy('stocks.sort', DESC);
        };


        //
        $query = $this->query()->with([
            'bundle.activeStocks',
            'alternative.variance.activeStocks',
            'brand',
            'category',
            'media'

        ]);

        if (is_numeric($product)) {
            return $query->findOrFail($product);
        }

        return $query->where('slug', '=', $product)
            ->firstorfail();
    }






    public function getProductDetailsByOldId($oldId)
    {

        $productModel = MigrationProduct::where('oldProductId', $oldId)->firstOrFail();

        $product = $productModel->productId;

        $with = [
            'variationsAttributes',

            'bundles',
            'bundles.variance',
            'bundles.variance.media',
            'bundles.variance.attributes',
            'bundles.variance.attributes.options',



            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.attributesWithValue',
            'variance.activeStock',
            'variance.activeStock.supplier',
            'variance.media',
            'brand',
            'brand.media',
            'suggested',
            'attributes',
            'media',
            'categories',
            "attributesWithValue",
            "variances",
            "variances.attributesValues",



        ];

        $productModel = $this->query()->with($with)->findOrFail($product);


        if (!$productModel->variance && ($productModel["type"] == "simple" || $productModel["type"] == "alternative") || !$productModel->bundle && $productModel["type"] == "bundle") {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }


        $variationsAttributes = $productModel->variationsAttributes->unique('attributeId');
        $selectedVariance = $productModel->variance;

        // Pre-load and group all variations attributes to avoid database queries in loops
        $allVariationsAttributes = $productModel->variationsAttributes;
        $variationsAttributesGrouped = $allVariationsAttributes->groupBy('varianceId');

        $variationsAttributes = $variationsAttributes->map(function ($item) use ($productModel, $selectedVariance, $allVariationsAttributes, $variationsAttributesGrouped) {
            $options = collect([]);

            // Get all unique options for this attribute using pre-loaded data
            $uniqueOptions = $allVariationsAttributes
                ->where('attributeId', $item->attributeId)
                ->unique('attributeOptionId');

            foreach ($uniqueOptions as $option) {

                if ($option->varianceId === $selectedVariance->varianceId) {

                    $option->variance = $selectedVariance->load(['activeStock', 'gallery']);
                    $option->activeStock = $selectedVariance->activeStock;
                    $option->gallery = $selectedVariance->gallery;

                } else {

                    $optionsSelectedExpectedOption = $selectedVariance->attributeValues
                        ->where('attributeId', '!=', $option->attributeId)
                        ->pluck('attributeOptionId')
                        ->toArray();

                    array_push($optionsSelectedExpectedOption, $option->attributeOptionId);

                    // Use pre-grouped data to avoid database queries
                    $matchingVariance = $variationsAttributesGrouped
                        ->filter(function ($varianceAttributes) use ($optionsSelectedExpectedOption) {
                            // Get all attribute option IDs for this variance
                            $varianceOptionIds = $varianceAttributes->pluck('attributeOptionId')->unique()->values()->toArray();

                            // Check if this variance has exactly the same attribute options as expected
                            return count($varianceOptionIds) === count($optionsSelectedExpectedOption) &&
                                count(array_diff($optionsSelectedExpectedOption, $varianceOptionIds)) === 0;
                        })
                        ->first();

                    // Load the variance with relations
                    if ($matchingVariance) {
                        // Get the first variance attribute to access the variance relationship
                        $firstVarianceAttribute = $matchingVariance->first();
                        $variance = $firstVarianceAttribute->variance->load(['activeStock', 'gallery']);
                        $option->variance = $variance;
                        $option->activeStock = $variance->activeStock ?? null;
                        $option->gallery = $variance->gallery ?? collect([]);
                    }

                }

                $options->push($option->toArray());
            }

            $item->options = $options;

            // Set the current value for this attribute using pre-loaded data
            $item->valueVariance = $allVariationsAttributes
                ->where('attributeId', $item->attributeId)
                ->where('varianceId', $productModel->variance->varianceId)
                ->first();

            return $item;
        });

        $productModel->variationsAttributes = $variationsAttributes;

        $productModel->paymentMethods = PaymentsMethod::whereNotIn('paymentMethodId', function ($q) use ($productModel) {
            $q->select('paymentMethodId')->from('product_payments')->where('productId', $productModel->productId);
        })->where('status', ACTIVE)->with(['media'])->get();






        return $productModel;
    }



    public function getProductDetailsByOldSlug($oldSlug)
    {

        $productModel = MigrationProduct::where('oldSlug', $oldSlug)->firstOrFail();

        $product = $productModel->productId;

        $with = [
            'variationsAttributes',



            'bundles',
            'bundles.variance',
            'bundles.variance.media',
            'bundles.variance.attributes',
            'bundles.variance.attributes.options',


            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.attributesWithValue',
            'variance.activeStock',
            'variance.activeStock.supplier',
            'variance.media',
            'brand',
            'brand.media',
            'suggested',
            'attributes',
            'media',
            'categories',
            "attributesWithValue",
            "variances",
            "variances.attributesValues",



        ];

        $productModel = $this->query()->with($with)->findOrFail($product);


        if (!$productModel->variance && ($productModel["type"] == "simple" || $productModel["type"] == "alternative") || !$productModel->bundle && $productModel["type"] == "bundle") {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }


        $variationsAttributes = $productModel->variationsAttributes->unique('attributeId');
        $selectedVariance = $productModel->variance;

        $variationsAttributes = $variationsAttributes->map(function ($item) use ($productModel, $selectedVariance) {
            $options = collect([]);

            // Get all unique options for this attribute
            $uniqueOptions = $productModel->variationsAttributes
                ->where('attributeId', $item->attributeId)
                ->unique('attributeOptionId');

            foreach ($uniqueOptions as $option) {

                if ($option->varianceId === $selectedVariance->varianceId) {

                    $option->variance = $selectedVariance->load(['activeStock', 'gallery']);
                    $option->activeStock = $selectedVariance->activeStock;
                    $option->gallery = $selectedVariance->gallery;

                } else {

                    $optionsSelectedExpectedOption = $selectedVariance->attributeValues
                        ->where('attributeId', '!=', $option->attributeId)
                        ->pluck('attributeOptionId')
                        ->toArray();

                    array_push($optionsSelectedExpectedOption, $option->attributeOptionId);

                    $matchingVariance = $productModel->variationsAttributes()
                        ->select('variances.varianceId')
                        ->whereIn('attributes_values.attributeOptionId', $optionsSelectedExpectedOption)
                        ->groupBy('variances.varianceId')
                        ->havingRaw('COUNT(DISTINCT attributes_values.attributeOptionId) = ?', [count($optionsSelectedExpectedOption)])
                        ->first();

                    // Load the variance with relations
                    if ($matchingVariance) {
                        $variance = $matchingVariance->variance->load(['activeStock', 'gallery']);
                        $option->variance = $variance;
                        $option->activeStock = $variance->activeStock ?? null;
                        $option->gallery = $variance->gallery ?? null;
                    }

                }

                $options->push($option->toArray());
            }

            $item->options = $options;

            // Set the current value for this attribute
            $item->valueVariance = $productModel->variationsAttributes
                ->where('attributeId', $item->attributeId)
                ->where('varianceId', $productModel->variance->varianceId)
                ->first();

            return $item;
        });

        $productModel->variationsAttributes = $variationsAttributes;

        $productModel->paymentMethods = PaymentsMethod::whereNotIn('paymentMethodId', function ($q) use ($productModel) {
            $q->select('paymentMethodId')->from('product_payments')->where('productId', $productModel->productId);
        })->where('status', ACTIVE)->with(['media'])->get();


        return $productModel;
    }





    private function productDetails($product, $variance = null, $attributeValue = null)
    {


        $with = [
            'variationsAttributes',
            'variationsAttributes.attribute',
            'variationsAttributes.option',
            'variationsAttributes.variance.activeStock',
            'variationsAttributes.variance.gallery',
            'variationsAttributes.variance.media',
            'variances',
            'variances.attributesValues',
            'variances.activeStock',
            'variance' => function ($query) use ($variance, $attributeValue) {
                if (!is_null($attributeValue)) {
                    $query->where('varianceId', $attributeValue->varianceId);
                } elseif (!is_null($variance) && $variance !== '') {
                    if (containsArabic($variance)) {
                        $query->whereJsonContains('oldSlug->' . current_locale(), $variance);
                    } else {
                        $query->where('slug', '=', $variance);
                    }
                } else {
                    $query->where('isDefault', true);
                }
            },
            'variance.attributesWithValue',
            'variance.activeStock',
            'variance.activeStock.supplier',
            'variance.media',
            'brand',
            'brand.media',
            'suggested',
            'attributes',
            'media',
            'categories',
            "attributesWithValue",
            // 'bundles',
            // 'bundles.variance',
            // 'bundles.variance.media',
            // 'bundles.variance.attributes',
            // 'bundles.variance.attributesWithValue',
            'activeStock',
        ];


        $query = $this->query()->with($with);

        if (is_numeric($product)) {
            $productModel = $query->findOrFail($product);
        } else {
            $queryJsonSlug = clone $query;
            $productModel = $query->where('slug', '=', $product)->first();

            if (!$productModel) {
                $productModel = $queryJsonSlug->whereJsonContains('oldSlug->' . current_locale(), $product)->first();
                if ($productModel) {
                    $redirectUrl = $productModel->slug;
                    throw new \App\Exceptions\ModelNotFoundException('Product found by oldSlug, redirecting...', 301, $redirectUrl);
                } else {
                    throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Product not found');
                }
            }

            if (!is_null($variance) && $variance !== '' && is_null($productModel->variance) && ($productModel["type"] == "simple" || $productModel["type"] == "alternative")) {
                $with['variance'] = function ($query) use ($variance) {
                    $query->whereJsonContains('oldSlug->' . current_locale(), $variance);
                };
                $productModel = $this->query()->with($with)->where('slug', '=', $product)->first();
                if ($productModel) {
                    $redirectUrl = $productModel->slug;
                    throw new \App\Exceptions\ModelNotFoundException('Product found by oldSlug, redirecting...', 301, $redirectUrl);
                } else {
                    throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Product not found');
                }
            }
        }

        if (!$productModel->variance && ($productModel["type"] == "simple" || $productModel["type"] == "alternative") || !$productModel->bundle && $productModel["type"] == "bundle") {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException();
        }

        // Process variations attributes with eager loaded data
        $variationsAttributesCollection = $productModel->variationsAttributes;
        $variationsAttributes = $productModel->variationsAttributes->unique('attributeId');
        $selectedVariance = $productModel->variance;

        $variationsAttributes = $variationsAttributes->map(function ($item) use ($productModel, $selectedVariance, $variationsAttributesCollection) {
            $options = collect([]);
            $uniqueOptions = $productModel->variationsAttributes
                ->where('attributeId', $item->attributeId)
                ->unique('attributeOptionId');

            foreach ($uniqueOptions as $option) {
                if ($option->varianceId === $selectedVariance->varianceId) {
                    $option->variance = $selectedVariance;
                    $option->activeStock = $selectedVariance->activeStock;
                    $option->gallery = $selectedVariance->media ?? collect([]);
                } else {
                    $optionsSelectedExpectedOption = $selectedVariance->attributeValues
                        ->where('attributeId', '!=', $option->attributeId)
                        ->pluck('attributeOptionId')
                        ->toArray();

                    array_push($optionsSelectedExpectedOption, $option->attributeOptionId);

                    // $matchingVariance = $productModel->variationsAttributes()
                    //     ->select('variances.varianceId')
                    //     ->whereIn('attributes_values.attributeOptionId', $optionsSelectedExpectedOption)
                    //     ->groupBy('variances.varianceId')
                    //     ->havingRaw('COUNT(DISTINCT attributes_values.attributeOptionId) = ?', [count($optionsSelectedExpectedOption)])
                    //     ->first();
                    // Group variations attributes by varianceId and find matching variance
                    $matchingVariance = $variationsAttributesCollection
                        ->groupBy('varianceId')
                        ->filter(function ($varianceAttributes) use ($optionsSelectedExpectedOption) {
                            // Get all attribute option IDs for this variance
                            $varianceOptionIds = $varianceAttributes->pluck('attributeOptionId')->unique()->values()->toArray();

                            // Check if this variance has exactly the same attribute options as expected
                            return count($varianceOptionIds) === count($optionsSelectedExpectedOption) &&
                                count(array_diff($optionsSelectedExpectedOption, $varianceOptionIds)) === 0;
                        })
                        ->first();

                    // Load the variance with relations
                    if ($matchingVariance) {
                        // Get the first variance attribute to access the variance relationship
                        $firstVarianceAttribute = $matchingVariance->first();
                        $variance = $firstVarianceAttribute->variance->load(['activeStock', 'gallery']);
                        $option->variance = $variance;
                        $option->activeStock = $variance->activeStock ?? null;
                        $option->gallery = $variance->gallery ?? collect([]);
                    }
                }
                $options->push($option->toArray());
            }

            $item->options = $options;
            $item->valueVariance = $productModel->variationsAttributes
                ->where('attributeId', $item->attributeId)
                ->where('varianceId', $productModel->variance->varianceId)
                ->first();

            return $item;
        });



        $productModel->variationsAttributes = $variationsAttributes;

        $productModel->paymentMethods = PaymentsMethod::whereNotIn('paymentMethodId', function ($q) use ($productModel) {
            $q->select('paymentMethodId')->from('product_payments')->where('productId', $productModel->productId);
        })->where('status', ACTIVE)->with(['media'])->get();

        DB::table('products')
            ->where('productId', $productModel->productId)
            ->increment('viewCount');

        return $productModel;
    }
    public function getProductDetails(int|string $product, int|string $variance = null)
    {
        $productModel = $this->productDetails($product, $variance);

        try {
            if ($user = getUserCached()) {
                $modelName = $user->getMorphClass();
                ProductVisit::insert([
                    'userId' => $modelName === User::class ? $user->userId : null,
                    'visitorId' => $modelName === Visitor::class ? $user->visitorId : null,
                    'productId' => $productModel->productId,
                    'ipAddress' => request()->ip(),
                    'userAgent' => user_agent()
                ]);
            }
        } catch (\Exception $e) {
            // Log error if needed
        }

        return $productModel;
    }



    public function getProductVarianceDetails($data)
    {


        $attributeValueQuery = AttributesValue::select('varianceId', DB::raw('COUNT(varianceId) AS numberOfVarianceId'))
            ->whereIn('attributeOptionId', $data['attributesOptions'])
            ->whereIn('attributes_values.varianceId', function ($query) use ($data) {
                $query->select('varianceId')->from('variances')->where('productId', $data['productId'])->whereNull('variances.deletedAt');
            })
            ->groupBy('varianceId');
        //
        if (count($data['attributesOptions']) > 1) {
            $attributeValueQuery->having('numberOfVarianceId', '>=', count($data['attributesOptions']));
            $attributeValueQuery->orderBy('numberOfVarianceId', 'DESC');
        }

        $attributeValue = $attributeValueQuery->first();

        $product = (int) $data['productId'];

        $productModel = $this->productDetails($product, null, $attributeValue);


        return $productModel;

    }


    public function storeRatingOnProduct(int|string $product, array $data)
    {
        $user = auth(GUARD_API)->user()->userId;

        $rating = Rating::create([
            'review' => $data['review'],
            'rating' => $data['rating'],
            'userId' => $user,
            'productId' => $product,
        ]);

        return $rating;
    }

    public function getRatings(int|string $product)
    {
        $ratings = resolve(RatingRepositoryInterface::class)->getRatingsProductById($product);

        return $ratings;
    }

    public function getRecentlyAddedProducts()
    {
        return $this->query()->with(['ratings.user:userId,lastName,firstName', 'price.currency', 'productsMedia.media'])->orderBy('productId', '')->limit(23)->get(['productId', 'name']);
    }

    public function getProductsByCategory(int|string $category, int|string $subcategory = null): mixed
    {
        $date = Carbon::now();
        $with = [



            'default' => function ($query) {
                $query->where('isDefault', true);
            },
            'default.stocks' => function ($query) use ($date) {
                $query->where('stocks.quantity', '>', 0)
                    ->where('stocks.quantity', '>', 'stocks.sold')
                    ->where('stocks.isPublished', PUBLISHED)
                    ->where('stocks.publishedAt', '<=', $date)
                    ->where('stocks.unPublishedAt', '>=', $date)
                    ->orderBy('stocks.sort', DESC);
            },
            'default.attributes',
            'default.attributes.options',
            'default.media',
            'bundles',
            'bundles.variance',
            'bundles.variance.media',
            'bundles.variance.attributes',
            'bundles.variance.attributes.options',


            'brand',
            'categories',
            'media',
        ];

        if (is_null($subcategory)) {
            $query = $this->query()->whereHas('categories', function ($query) use ($category) {
                $query->where('slug', '=', $category);
            });
        } else {
            $query = $this->query()->whereHas('categories', function ($query) use ($subcategory) {
                $query->where('slug', '=', $subcategory);
            });
        }


        return $query->with($with)->filters()->customPaginate();
    }






    public function mostPopular()
    {

        $with = [
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.attributes',
            'variance.attributes.options',
            'variance.activeStock',
            'variance.media',
            'brand',
            'categories',
            'media',
        ];

        return $this->query()
            ->whereHas('variance', function ($query) {
                $query->where('isDefault', true)
                    ->whereHas('activeStock', function ($query) {
                        $query->whereNotNull('stocks.stockId'); // Ensures related stock exists
                    });
            })
            ->where('isListed', true)
            ->inRandomOrder()
            ->orderBy('priority', "DESC")
            ->orderBy('createdAt', 'DESC')
            ->orderBy('avgRate', 'DESC')
            ->limit(20)
            ->with($with)
            ->get();

    }

    public function newArrival()
    {

        $with = [
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.attributes',
            'variance.attributes.options',
            'variance.activeStock',
            'variance.media',
            'brand',
            'brand.media',
            'categories',
            'categories.media',
            'media',
        ];

        return $this->query()
            ->whereHas('variance', function ($query) {
                $query->where('isDefault', true)
                    ->whereHas('activeStock', function ($query) {
                        $query->whereNotNull('stocks.stockId'); // Ensures related stock exists
                    });
            })
            ->where('isListed', true)
            ->inRandomOrder()
            ->orderBy('priority', "DESC")
            ->orderBy('createdAt', 'DESC')
            ->orderBy('avgRate', 'DESC')
            ->limit(20)
            ->with($with)
            ->get();
    }



    public function suggestedProduct($id)
    {


        $with = [
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.attributes',
            'variance.attributes.options',
            'variance.activeStock',
            'variance.media',
            'brand',
            'brand.media',
            'categories.media',
            'categories',
            'media',
        ];

        return $this->query()
            ->whereHas('variance', function ($query) {
                $query->where('isDefault', true)
                    ->whereHas('activeStock', function ($query) {
                        $query->whereNotNull('stocks.stockId'); // Ensures related stock exists
                    });
            })


            ->whereIn('productId', function ($query) use ($id) {
                $query->select('p2.productId')
                    ->from('categories_product as cp1')
                    ->join('categories_product as cp2', 'cp1.categoryId', '=', 'cp2.categoryId')
                    ->join('products as p1', 'cp1.productId', '=', 'p1.productId')
                    ->join('products as p2', 'cp2.productId', '=', 'p2.productId')
                    ->where('p1.productId', $id)
                    ->where('p2.productId', '!=', $id)
                    ->groupBy('p2.productId');
            })
            ->inRandomOrder()
            ->orderBy('priority', "DESC")
            ->orderBy('createdAt', 'DESC')
            ->orderBy('avgRate', 'DESC')
            ->limit(20)
            ->with($with)
            ->get();
    }






    public function updateRatingOnProduct(int|string $product, int|string $ratingId, array $data)
    {

        $rating = tap(Rating::find($ratingId), function ($rating) use ($data) {
            $rating->update([
                'review' => $data['review'],
                'rating' => $data['rating'],
            ]);
        })->refresh();

        return $rating;
    }






}